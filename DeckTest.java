import java.util.List;
import java.util.HashSet;
import java.util.Set;

/**
 * Test class for the Deck of Cards Simulator.
 * Tests all major functionality and edge cases.
 * Note: This version doesn't require JUnit - it's a standalone test class.
 */
public class DeckTest {

    private static int testsRun = 0;
    private static int testsPassed = 0;

    public static void main(String[] args) {
        System.out.println("=== Deck Test Suite ===\n");

        DeckTest tester = new DeckTest();
        tester.runAllTests();

        System.out.println("\n=== Test Results ===");
        System.out.println("Tests run: " + testsRun);
        System.out.println("Tests passed: " + testsPassed);
        System.out.println("Tests failed: " + (testsRun - testsPassed));

        if (testsPassed == testsRun) {
            System.out.println("✅ All tests passed!");
        } else {
            System.out.println("❌ Some tests failed!");
        }
    }

    private void runAllTests() {
    
    @Test
    void testInitialDeckSize() {
        assertEquals(52, deck.size(), "Initial deck should have 52 cards");
        assertFalse(deck.isEmpty(), "Initial deck should not be empty");
    }
    
    @Test
    void testInitialDeckContainsAllCards() {
        List<Card> cards = deck.getCards();
        Set<Card> uniqueCards = new HashSet<>(cards);
        
        assertEquals(52, uniqueCards.size(), "Deck should contain 52 unique cards");
        assertEquals(52, cards.size(), "Deck should have exactly 52 cards");
    }
    
    @Test
    void testDrawSingleCard() {
        Card drawnCard = deck.drawCard();
        
        assertNotNull(drawnCard, "Drawn card should not be null");
        assertEquals(51, deck.size(), "Deck should have 51 cards after drawing one");
    }
    
    @Test
    void testDrawMultipleCards() {
        List<Card> drawnCards = deck.drawCards(5);
        
        assertEquals(5, drawnCards.size(), "Should draw exactly 5 cards");
        assertEquals(47, deck.size(), "Deck should have 47 cards after drawing 5");
        
        // Check that all drawn cards are unique
        Set<Card> uniqueCards = new HashSet<>(drawnCards);
        assertEquals(5, uniqueCards.size(), "All drawn cards should be unique");
    }
    
    @Test
    void testDrawFromEmptyDeck() {
        // Draw all cards
        deck.drawCards(52);
        assertTrue(deck.isEmpty(), "Deck should be empty after drawing all cards");
        
        // Try to draw from empty deck
        assertThrows(IllegalStateException.class, () -> deck.drawCard(),
                "Drawing from empty deck should throw IllegalStateException");
    }
    
    @Test
    void testDrawNegativeCards() {
        assertThrows(IllegalArgumentException.class, () -> deck.drawCards(-1),
                "Drawing negative number of cards should throw IllegalArgumentException");
    }
    
    @Test
    void testDrawMoreCardsThanAvailable() {
        assertThrows(IllegalArgumentException.class, () -> deck.drawCards(100),
                "Drawing more cards than available should throw IllegalArgumentException");
    }
    
    @Test
    void testShuffle() {
        List<Card> originalOrder = deck.getCards();
        deck.shuffle();
        List<Card> shuffledOrder = deck.getCards();
        
        assertEquals(52, deck.size(), "Deck size should remain 52 after shuffle");
        
        // Check that the order has changed (very unlikely to be the same after shuffle)
        boolean orderChanged = false;
        for (int i = 0; i < originalOrder.size(); i++) {
            if (!originalOrder.get(i).equals(shuffledOrder.get(i))) {
                orderChanged = true;
                break;
            }
        }
        assertTrue(orderChanged, "Shuffle should change the order of cards");
    }
    
    @Test
    void testSort() {
        deck.shuffle(); // First shuffle to randomize
        deck.sort();
        
        List<Card> cards = deck.getCards();
        
        // Check that cards are sorted by suit first, then rank
        for (int i = 0; i < cards.size() - 1; i++) {
            Card current = cards.get(i);
            Card next = cards.get(i + 1);
            
            assertTrue(current.compareTo(next) <= 0,
                    "Cards should be in sorted order: " + current + " should come before or equal " + next);
        }
    }
    
    @Test
    void testSortByRank() {
        deck.shuffle(); // First shuffle to randomize
        deck.sortByRank();
        
        List<Card> cards = deck.getCards();
        
        // Check that cards are sorted by rank first
        for (int i = 0; i < cards.size() - 1; i++) {
            Card current = cards.get(i);
            Card next = cards.get(i + 1);
            
            assertTrue(current.getRank().getValue() <= next.getRank().getValue(),
                    "Cards should be sorted by rank: " + current.getRank() + " should come before or equal " + next.getRank());
        }
    }
    
    @Test
    void testReset() {
        // Draw some cards and shuffle
        deck.drawCards(10);
        deck.shuffle();
        
        assertEquals(42, deck.size(), "Deck should have 42 cards after drawing 10");
        
        // Reset the deck
        deck.reset();
        
        assertEquals(52, deck.size(), "Deck should have 52 cards after reset");
        assertFalse(deck.isEmpty(), "Deck should not be empty after reset");
        
        // Check that all 52 unique cards are present
        List<Card> cards = deck.getCards();
        Set<Card> uniqueCards = new HashSet<>(cards);
        assertEquals(52, uniqueCards.size(), "Reset deck should contain 52 unique cards");
    }
    
    @Test
    void testCardEquality() {
        Card card1 = new Card(Suit.HEARTS, Rank.ACE);
        Card card2 = new Card(Suit.HEARTS, Rank.ACE);
        Card card3 = new Card(Suit.SPADES, Rank.ACE);
        
        assertEquals(card1, card2, "Cards with same suit and rank should be equal");
        assertNotEquals(card1, card3, "Cards with different suits should not be equal");
    }
    
    @Test
    void testCardToString() {
        Card card = new Card(Suit.HEARTS, Rank.ACE);
        assertEquals("Ace of Hearts", card.toString(), "Card toString should return 'Rank of Suit'");
    }
}

import java.util.List;

/**
 * Interface defining the core operations of an elevator system
 */
public interface ElevatorOperations {
    
    /**
     * Add a new request to the elevator
     * @param floor The target floor
     * @param type The type of request (internal/external)
     * @return true if request was added, false if duplicate
     */
    boolean addRequest(int floor, RequestType type);
    
    /**
     * Move the elevator and process requests
     * This method handles the main simulation logic
     */
    void move();
    
    /**
     * Process the current floor (handle stops)
     * @throws InterruptedException if sleep is interrupted
     */
    void processFloor() throws InterruptedException;
    
    /**
     * Get the current status of the elevator
     * @return String representation of elevator status
     */
    String getStatus();
    
    /**
     * Get the current floor
     * @return current floor number
     */
    int getCurrentFloor();
    
    /**
     * Get the current direction
     * @return current direction
     */
    Direction getDirection();
    
    /**
     * Check if elevator is currently moving
     * @return true if moving, false if idle
     */
    boolean isMoving();
    
    /**
     * Get list of pending requests
     * @return list of pending floor numbers
     */
    List<Integer> getPendingRequests();
    
    /**
     * Check if elevator has any pending requests
     * @return true if there are pending requests
     */
    boolean hasPendingRequests();
}

import java.util.*;
import java.util.stream.Collectors;

enum Direction {
    UP,
    DOWN,
    IDLE
}

enum RequestType {
    INTERNAL,
    EXTERNAL_UP,
    EXTERNAL_DOWN
}

class ElevatorRequest implements Comparable<ElevatorRequest> {
    private final int floor;
    private final RequestType type;
    private final long timestamp;

    public ElevatorRequest(int floor, RequestType type) {
        this.floor = floor;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
    }

    public int getFloor() {
        return floor;
    }

    public RequestType getType() {
        return type;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public int compareTo(ElevatorRequest other) {
        int floorComparison = Integer.compare(this.floor, other.floor);
        if (floorComparison != 0) {
            return floorComparison;
        }
        return Long.compare(this.timestamp, other.timestamp);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ElevatorRequest request = (ElevatorRequest) obj;
        return floor == request.floor && type == request.type;
    }

    @Override
    public int hashCode() {
        return floor * 31 + type.hashCode();
    }

    @Override
    public String toString() {
        return String.format("Floor %d (%s)", floor, type);
    }
}

interface ElevatorOperations {
    boolean addRequest(int floor, RequestType type);
    void move();
    void processFloor() throws InterruptedException;
    String getStatus();
    int getCurrentFloor();
    Direction getDirection();
    boolean isMoving();
    List<Integer> getPendingRequests();
    boolean hasPendingRequests();
}

class Elevator implements ElevatorOperations {
    private static final int MIN_FLOOR = 1;
    private static final int MAX_FLOOR = 20;
    private static final int STOP_DURATION_MS = 2000;
    
    private int currentFloor;
    private Direction direction;
    private boolean isMoving;
    private final Set<ElevatorRequest> requestSet;
    private final PriorityQueue<ElevatorRequest> upRequests;
    private final PriorityQueue<ElevatorRequest> downRequests;
    
    public Elevator() {
        this(1);
    }
    
    public Elevator(int startingFloor) {
        if (startingFloor < MIN_FLOOR || startingFloor > MAX_FLOOR) {
            throw new IllegalArgumentException("Starting floor must be between " + MIN_FLOOR + " and " + MAX_FLOOR);
        }
        
        this.currentFloor = startingFloor;
        this.direction = Direction.IDLE;
        this.isMoving = false;
        this.requestSet = new HashSet<>();
        
        this.upRequests = new PriorityQueue<>(Comparator.comparingInt(ElevatorRequest::getFloor));
        
        this.downRequests = new PriorityQueue<>((a, b) -> Integer.compare(b.getFloor(), a.getFloor()));
        
        System.out.println("[LOG] Elevator initialized at Floor " + currentFloor);
    }
    
    @Override
    public boolean addRequest(int floor, RequestType type) {
        if (floor < MIN_FLOOR || floor > MAX_FLOOR) {
            System.out.println("[ERROR] Invalid floor: " + floor + ". Must be between " + MIN_FLOOR + " and " + MAX_FLOOR);
            return false;
        }
        
        if (floor == currentFloor && !isMoving) {
            System.out.println("[LOG] Already at Floor " + floor + " - Request ignored");
            return false;
        }
        
        ElevatorRequest request = new ElevatorRequest(floor, type);
        
        if (requestSet.contains(request)) {
            System.out.println("[LOG] Duplicate request ignored: Floor " + floor);
            return false;
        }
        
        requestSet.add(request);
        
        if (floor > currentFloor) {
            upRequests.offer(request);
        } else {
            downRequests.offer(request);
        }
        
        System.out.println("[LOG] New request added: Floor " + floor + " (" + type + ")");
        return true;
    }
    
    @Override
    public void move() {
        if (!hasPendingRequests()) {
            if (isMoving) {
                isMoving = false;
                direction = Direction.IDLE;
                System.out.println("[LOG] IDLE at Floor " + currentFloor);
            }
            return;
        }
        
        determineDirection();
        isMoving = true;
        
        int targetFloor = getNextTargetFloor();
        
        if (targetFloor == currentFloor) {
            try {
                processFloor();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("[ERROR] Elevator operation interrupted");
            }
        } else {
            if (direction == Direction.UP) {
                currentFloor++;
                System.out.println("[LOG] Floor " + currentFloor + ": " + 
                    (shouldStopAtCurrentFloor() ? "Stopped" : "Passing by") + 
                    " (Going " + direction + ") | Pending: " + getPendingRequests());
            } else if (direction == Direction.DOWN) {
                currentFloor--;
                System.out.println("[LOG] Floor " + currentFloor + ": " + 
                    (shouldStopAtCurrentFloor() ? "Stopped" : "Passing by") + 
                    " (Going " + direction + ") | Pending: " + getPendingRequests());
            }
            
            if (shouldStopAtCurrentFloor()) {
                try {
                    processFloor();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.out.println("[ERROR] Elevator operation interrupted");
                }
            }
        }
    }
    
    @Override
    public void processFloor() throws InterruptedException {
        removeRequestsForCurrentFloor();
        
        if (requestSet.isEmpty()) {
            return;
        }
        
        System.out.println("[LOG] Stopping for 2 seconds...");
        Thread.sleep(STOP_DURATION_MS);
    }
    
    private void determineDirection() {
        if (direction == Direction.IDLE || !hasRequestsInCurrentDirection()) {
            if (!upRequests.isEmpty() && upRequests.peek().getFloor() >= currentFloor) {
                direction = Direction.UP;
            } else if (!downRequests.isEmpty() && downRequests.peek().getFloor() <= currentFloor) {
                direction = Direction.DOWN;
            } else if (!upRequests.isEmpty()) {
                direction = Direction.UP;
            } else if (!downRequests.isEmpty()) {
                direction = Direction.DOWN;
            } else {
                direction = Direction.IDLE;
            }
        }
    }
    
    private boolean hasRequestsInCurrentDirection() {
        if (direction == Direction.UP) {
            return upRequests.stream().anyMatch(req -> req.getFloor() > currentFloor);
        } else if (direction == Direction.DOWN) {
            return downRequests.stream().anyMatch(req -> req.getFloor() < currentFloor);
        }
        return false;
    }
    
    private int getNextTargetFloor() {
        if (direction == Direction.UP && !upRequests.isEmpty()) {
            return upRequests.peek().getFloor();
        } else if (direction == Direction.DOWN && !downRequests.isEmpty()) {
            return downRequests.peek().getFloor();
        }
        return currentFloor;
    }
    
    private boolean shouldStopAtCurrentFloor() {
        return requestSet.stream().anyMatch(req -> req.getFloor() == currentFloor);
    }
    
    private void removeRequestsForCurrentFloor() {
        requestSet.removeIf(req -> req.getFloor() == currentFloor);
        upRequests.removeIf(req -> req.getFloor() == currentFloor);
        downRequests.removeIf(req -> req.getFloor() == currentFloor);
    }
    
    @Override
    public String getStatus() {
        return String.format("Floor %d | Direction: %s | Moving: %s | Pending: %s", 
            currentFloor, direction, isMoving, getPendingRequests());
    }
    
    @Override
    public int getCurrentFloor() {
        return currentFloor;
    }
    
    @Override
    public Direction getDirection() {
        return direction;
    }
    
    @Override
    public boolean isMoving() {
        return isMoving;
    }
    
    @Override
    public List<Integer> getPendingRequests() {
        return requestSet.stream()
            .map(ElevatorRequest::getFloor)
            .sorted()
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasPendingRequests() {
        return !requestSet.isEmpty();
    }
}

public class ElevatorProject {
    private static final int SIMULATION_DELAY_MS = 1000;

    public static void main(String[] args) {
        System.out.println("===== Single-Threaded Elevator Simulation =====");

        runScenario1();
        System.out.println("\n" + "=".repeat(50) + "\n");
        runScenario2();
        System.out.println("\n" + "=".repeat(50) + "\n");
        runScenario3();

        System.out.println("\n" + "=".repeat(50));
        System.out.println("Starting Interactive Mode...");
        runInteractiveMode();
    }

    private static void runScenario1() {
        System.out.println("--- Scenario 1: Basic Movement ---");
        Elevator elevator = new Elevator(1);

        elevator.addRequest(5, RequestType.INTERNAL);
        elevator.addRequest(3, RequestType.EXTERNAL_DOWN);
        elevator.addRequest(7, RequestType.INTERNAL);

        runSimulation(elevator, "Scenario 1");
    }

    private static void runScenario2() {
        System.out.println("--- Scenario 2: Complex Multi-Directional ---");
        Elevator elevator = new Elevator(10);

        elevator.addRequest(15, RequestType.INTERNAL);
        elevator.addRequest(5, RequestType.EXTERNAL_UP);
        elevator.addRequest(18, RequestType.INTERNAL);
        elevator.addRequest(2, RequestType.EXTERNAL_DOWN);
        elevator.addRequest(12, RequestType.EXTERNAL_UP);

        runSimulation(elevator, "Scenario 2");
    }

    private static void runScenario3() {
        System.out.println("--- Scenario 3: Duplicate Request Handling ---");
        Elevator elevator = new Elevator(5);

        elevator.addRequest(8, RequestType.INTERNAL);
        elevator.addRequest(8, RequestType.INTERNAL);
        elevator.addRequest(3, RequestType.EXTERNAL_DOWN);
        elevator.addRequest(8, RequestType.EXTERNAL_UP);
        elevator.addRequest(12, RequestType.INTERNAL);

        runSimulation(elevator, "Scenario 3");
    }

    private static void runSimulation(Elevator elevator, String scenarioName) {
        System.out.println("Starting " + scenarioName + "...");
        System.out.println("Initial Status: " + elevator.getStatus());
        System.out.println();

        int moveCount = 0;
        int maxMoves = 100;

        while (elevator.hasPendingRequests() && moveCount < maxMoves) {
            try {
                elevator.move();
                Thread.sleep(SIMULATION_DELAY_MS);
                moveCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("[ERROR] Simulation interrupted");
                break;
            }
        }

        if (moveCount >= maxMoves) {
            System.out.println("[WARNING] Simulation stopped - maximum moves reached");
        }

        System.out.println("\n" + scenarioName + " completed!");
        System.out.println("Final Status: " + elevator.getStatus());
        System.out.println("Total moves: " + moveCount);
    }

    private static void runInteractiveMode() {
        Elevator elevator = new Elevator(1);
        Scanner scanner = new Scanner(System.in);

        System.out.println("Interactive Elevator Simulation");
        System.out.println("Commands:");
        System.out.println("  'r <floor>' - Add internal request");
        System.out.println("  'u <floor>' - Add external up request");
        System.out.println("  'd <floor>' - Add external down request");
        System.out.println("  's' - Show status");
        System.out.println("  'm' - Move elevator one step");
        System.out.println("  'auto' - Auto-run until all requests processed");
        System.out.println("  'q' - Quit");
        System.out.println();

        boolean running = true;
        while (running) {
            System.out.print("Enter command: ");
            String input = scanner.nextLine().trim().toLowerCase();

            try {
                if (input.equals("q")) {
                    running = false;
                } else if (input.equals("s")) {
                    System.out.println("Status: " + elevator.getStatus());
                } else if (input.equals("m")) {
                    elevator.move();
                } else if (input.equals("auto")) {
                    System.out.println("Auto-running simulation...");
                    runSimulation(elevator, "Interactive Auto-Run");
                } else if (input.startsWith("r ")) {
                    int floor = Integer.parseInt(input.substring(2));
                    elevator.addRequest(floor, RequestType.INTERNAL);
                } else if (input.startsWith("u ")) {
                    int floor = Integer.parseInt(input.substring(2));
                    elevator.addRequest(floor, RequestType.EXTERNAL_UP);
                } else if (input.startsWith("d ")) {
                    int floor = Integer.parseInt(input.substring(2));
                    elevator.addRequest(floor, RequestType.EXTERNAL_DOWN);
                } else {
                    System.out.println("Unknown command. Type 'q' to quit.");
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid floor number. Please enter a valid integer.");
            } catch (Exception e) {
                System.out.println("Error: " + e.getMessage());
            }

            System.out.println();
        }

        scanner.close();
        System.out.println("Simulation ended. Thank you!");
    }
}

import java.util.*;
import java.util.stream.Collectors;

public class Elevator implements ElevatorOperations {
    private static final int MIN_FLOOR = 1;
    private static final int MAX_FLOOR = 20;
    private static final int STOP_DURATION_MS = 2000;

    private int currentFloor;
    private Direction direction;
    private boolean isMoving;
    private final Set<ElevatorRequest> requestSet;
    private final PriorityQueue<ElevatorRequest> upRequests;
    private final PriorityQueue<ElevatorRequest> downRequests;

    public Elevator() {
        this(1);
    }
    
    public Elevator(int startingFloor) {
        if (startingFloor < MIN_FLOOR || startingFloor > MAX_FLOOR) {
            throw new IllegalArgumentException("Starting floor must be between " + MIN_FLOOR + " and " + MAX_FLOOR);
        }

        this.currentFloor = startingFloor;
        this.direction = Direction.IDLE;
        this.isMoving = false;
        this.requestSet = new HashSet<>();

        this.upRequests = new PriorityQueue<>(Comparator.comparingInt(ElevatorRequest::getFloor));

        this.downRequests = new PriorityQueue<>((a, b) -> Integer.compare(b.getFloor(), a.getFloor()));

        System.out.println("[LOG] Elevator initialized at Floor " + currentFloor);
    }
    
    @Override
    public boolean addRequest(int floor, RequestType type) {
        if (floor < MIN_FLOOR || floor > MAX_FLOOR) {
            System.out.println("[ERROR] Invalid floor: " + floor + ". Must be between " + MIN_FLOOR + " and " + MAX_FLOOR);
            return false;
        }
        
        if (floor == currentFloor && !isMoving) {
            System.out.println("[LOG] Already at Floor " + floor + " - Request ignored");
            return false;
        }
        
        ElevatorRequest request = new ElevatorRequest(floor, type);
        
        // Check for duplicates
        if (requestSet.contains(request)) {
            System.out.println("[LOG] Duplicate request ignored: Floor " + floor);
            return false;
        }

        requestSet.add(request);

        if (floor > currentFloor) {
            upRequests.offer(request);
        } else {
            downRequests.offer(request);
        }
        
        System.out.println("[LOG] New request added: Floor " + floor + " (" + type + ")");
        return true;
    }
    
    @Override
    public void move() {
        if (!hasPendingRequests()) {
            if (isMoving) {
                isMoving = false;
                direction = Direction.IDLE;
                System.out.println("[LOG] IDLE at Floor " + currentFloor);
            }
            return;
        }
        
        determineDirection();
        isMoving = true;

        int targetFloor = getNextTargetFloor();

        if (targetFloor == currentFloor) {
            try {
                processFloor();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("[ERROR] Elevator operation interrupted");
            }
        } else {
            if (direction == Direction.UP) {
                currentFloor++;
                System.out.println("[LOG] Floor " + currentFloor + ": " +
                    (shouldStopAtCurrentFloor() ? "Stopped" : "Passing by") +
                    " (Going " + direction + ") | Pending: " + getPendingRequests());
            } else if (direction == Direction.DOWN) {
                currentFloor--;
                System.out.println("[LOG] Floor " + currentFloor + ": " +
                    (shouldStopAtCurrentFloor() ? "Stopped" : "Passing by") +
                    " (Going " + direction + ") | Pending: " + getPendingRequests());
            }

            if (shouldStopAtCurrentFloor()) {
                try {
                    processFloor();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.out.println("[ERROR] Elevator operation interrupted");
                }
            }
        }
    }
    
    @Override
    public void processFloor() throws InterruptedException {
        removeRequestsForCurrentFloor();

        if (requestSet.isEmpty()) {
            return;
        }

        System.out.println("[LOG] Stopping for 2 seconds...");
        Thread.sleep(STOP_DURATION_MS);
    }
    
    private void determineDirection() {
        if (direction == Direction.IDLE || !hasRequestsInCurrentDirection()) {
            if (!upRequests.isEmpty() && upRequests.peek().getFloor() >= currentFloor) {
                direction = Direction.UP;
            } else if (!downRequests.isEmpty() && downRequests.peek().getFloor() <= currentFloor) {
                direction = Direction.DOWN;
            } else if (!upRequests.isEmpty()) {
                direction = Direction.UP;
            } else if (!downRequests.isEmpty()) {
                direction = Direction.DOWN;
            } else {
                direction = Direction.IDLE;
            }
        }
    }
    
    private boolean hasRequestsInCurrentDirection() {
        if (direction == Direction.UP) {
            return upRequests.stream().anyMatch(req -> req.getFloor() > currentFloor);
        } else if (direction == Direction.DOWN) {
            return downRequests.stream().anyMatch(req -> req.getFloor() < currentFloor);
        }
        return false;
    }
    
    private int getNextTargetFloor() {
        if (direction == Direction.UP && !upRequests.isEmpty()) {
            return upRequests.peek().getFloor();
        } else if (direction == Direction.DOWN && !downRequests.isEmpty()) {
            return downRequests.peek().getFloor();
        }
        return currentFloor;
    }
    
    private boolean shouldStopAtCurrentFloor() {
        return requestSet.stream().anyMatch(req -> req.getFloor() == currentFloor);
    }
    
    private void removeRequestsForCurrentFloor() {
        requestSet.removeIf(req -> req.getFloor() == currentFloor);
        upRequests.removeIf(req -> req.getFloor() == currentFloor);
        downRequests.removeIf(req -> req.getFloor() == currentFloor);
    }
    
    @Override
    public String getStatus() {
        return String.format("Floor %d | Direction: %s | Moving: %s | Pending: %s", 
            currentFloor, direction, isMoving, getPendingRequests());
    }
    
    @Override
    public int getCurrentFloor() {
        return currentFloor;
    }
    
    @Override
    public Direction getDirection() {
        return direction;
    }
    
    @Override
    public boolean isMoving() {
        return isMoving;
    }
    
    @Override
    public List<Integer> getPendingRequests() {
        return requestSet.stream()
            .map(ElevatorRequest::getFloor)
            .sorted()
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean hasPendingRequests() {
        return !requestSet.isEmpty();
    }
}

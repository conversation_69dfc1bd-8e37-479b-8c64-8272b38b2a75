# Single-Threaded Elevator Simulation in Java

A comprehensive Java implementation of an elevator system simulation using Object-Oriented Programming principles.

## Project Structure

```
├── Direction.java           # Enum for elevator direction (UP, DOWN, IDLE)
├── RequestType.java         # Enum for request types (INTERNAL, EXTERNAL_UP, EXTERNA<PERSON>_DOWN)
├── ElevatorRequest.java     # Class representing elevator requests
├── ElevatorOperations.java  # Interface defining elevator operations
├── Elevator.java           # Main elevator implementation
├── ElevatorSimulation.java # Main simulation class with scenarios
├── ElevatorTest.java       # JUnit test class
└── README.md              # This file
```

## Features

### Core Features
- **Realistic Movement**: Elevator moves one floor at a time with 2-second stops
- **Direction Logic**: Continues in current direction until no more requests
- **Request Management**: Efficient handling using PriorityQueue with duplicate prevention
- **Multiple Request Types**: Internal (from inside) and External (floor buttons)
- **Comprehensive Logging**: Real-time status updates and movement tracking

### OOP Implementation
- **Interface-based Design**: `ElevatorOperations` interface for clean abstraction
- **Encapsulation**: All elevator internals properly encapsulated
- **Exception Handling**: Proper error handling for invalid inputs
- **Java Conventions**: Follows standard Java naming and coding conventions

### Advanced Features
- **Priority Queue Management**: Separate queues for up/down requests
- **Duplicate Prevention**: Uses HashSet to prevent duplicate requests
- **Interactive Mode**: Real-time request addition and control
- **Multiple Scenarios**: Pre-built simulation scenarios
- **JUnit Testing**: Comprehensive test suite

## How to Run

### Compile the Project
```bash
javac *.java
```

### Run the Simulation
```bash
java ElevatorSimulation
```

### Run Tests (requires JUnit 5)
```bash
# Add JUnit 5 to classpath and run
java -cp .:junit-platform-console-standalone-1.8.2.jar org.junit.platform.console.ConsoleLauncher --class-path . --scan-class-path
```

## Usage Examples

### Basic Usage
```java
// Create elevator starting at floor 1
Elevator elevator = new Elevator(1);

// Add requests
elevator.addRequest(5, RequestType.INTERNAL);
elevator.addRequest(3, RequestType.EXTERNAL_DOWN);

// Run simulation
while (elevator.hasPendingRequests()) {
    elevator.move();
    Thread.sleep(1000); // 1 second delay between moves
}
```

### Interactive Mode Commands
- `r <floor>` - Add internal request
- `u <floor>` - Add external up request  
- `d <floor>` - Add external down request
- `s` - Show current status
- `m` - Move elevator one step
- `auto` - Auto-run until all requests processed
- `q` - Quit

## Sample Output

```
===== Single-Threaded Elevator Simulation =====

--- Scenario 1: Basic Movement ---
[LOG] Elevator initialized at Floor 1
[LOG] New request added: Floor 5 (INTERNAL)
[LOG] New request added: Floor 3 (EXTERNAL_DOWN)
[LOG] New request added: Floor 7 (INTERNAL)
Starting Scenario 1...
Initial Status: Floor 1 | Direction: IDLE | Moving: false | Pending: [3, 5, 7]

[LOG] Floor 2: Passing by (Going UP) | Pending: [3, 5, 7]
[LOG] Floor 3: Stopped (Going UP) | Pending: [3, 5, 7]
[LOG] Stopping for 2 seconds...
[LOG] Floor 4: Passing by (Going UP) | Pending: [5, 7]
[LOG] Floor 5: Stopped (Going UP) | Pending: [5, 7]
[LOG] Stopping for 2 seconds...
[LOG] Floor 6: Passing by (Going UP) | Pending: [7]
[LOG] Floor 7: Stopped (Going UP) | Pending: [7]
[LOG] Stopping for 2 seconds...
[LOG] IDLE at Floor 7

Scenario 1 completed!
Final Status: Floor 7 | Direction: IDLE | Moving: false | Pending: []
Total moves: 7
```

## Architecture Details

### Request Management
- **PriorityQueue**: Separate queues for up and down requests
- **Duplicate Prevention**: HashSet ensures no duplicate requests
- **Request Types**: Support for internal and external (up/down) requests

### Movement Logic
- **Direction Persistence**: Continues in current direction until completion
- **Floor-by-Floor**: Moves one floor at a time for realistic simulation
- **Stop Processing**: 2-second pause at each requested floor

### Error Handling
- **Floor Validation**: Ensures requests are within valid range (1-20)
- **Duplicate Detection**: Prevents duplicate requests
- **Thread Interruption**: Proper handling of interrupted sleep

## Testing

The project includes comprehensive JUnit tests covering:
- Elevator initialization
- Request addition and validation
- Movement logic (up/down)
- Duplicate prevention
- Invalid input handling
- Status reporting
- Edge cases

## Customization

### Configuration Constants
```java
private static final int MIN_FLOOR = 1;
private static final int MAX_FLOOR = 20;
private static final int STOP_DURATION_MS = 2000;
```

### Adding New Request Types
Extend the `RequestType` enum and modify request handling logic accordingly.

### Multiple Elevator Support
The architecture supports extension to multiple elevators by creating an `ElevatorController` class.

## Future Enhancements

- **Express Elevators**: Different elevator types via inheritance
- **Visual Representation**: ASCII art display
- **Multiple Elevator Coordination**: Central dispatch system
- **Load Balancing**: Optimize elevator assignment
- **Maintenance Mode**: Elevator out-of-service handling

## Dependencies

- **Java 8+**: Core language features
- **JUnit 5**: For unit testing (optional)

## License

This project is created for educational purposes and demonstrates OOP principles in Java.

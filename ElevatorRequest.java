public class ElevatorRequest implements Comparable<ElevatorRequest> {
    private final int floor;
    private final RequestType type;
    private final long timestamp;

    public ElevatorRequest(int floor, RequestType type) {
        this.floor = floor;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
    }

    public int getFloor() {
        return floor;
    }

    public RequestType getType() {
        return type;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @Override
    public int compareTo(ElevatorRequest other) {
        int floorComparison = Integer.compare(this.floor, other.floor);
        if (floorComparison != 0) {
            return floorComparison;
        }
        return Long.compare(this.timestamp, other.timestamp);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ElevatorRequest request = (ElevatorRequest) obj;
        return floor == request.floor && type == request.type;
    }

    @Override
    public int hashCode() {
        return floor * 31 + type.hashCode();
    }

    @Override
    public String toString() {
        return String.format("Floor %d (%s)", floor, type);
    }
}

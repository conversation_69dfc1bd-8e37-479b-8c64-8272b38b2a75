import java.util.*;

enum Suit {
    HEARTS("Hearts"),
    DIAMONDS("Diamonds"), 
    SPADES("Spades"),
    CLUBS("Clubs");
    
    private final String displayName;
    
    Suit(String displayName) {
        this.displayName = displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}


enum Rank {
    ACE("Ace", 1),
    TW<PERSON>("2", 2),
    THRE<PERSON>("3", 3),
    FOUR("4", 4),
    <PERSON>IVE("5", 5),
    <PERSON>IX("6", 6),
    SEVEN("7", 7),
    EIGHT("8", 8),
    NINE("9", 9),
    <PERSON><PERSON>("10", 10),
    <PERSON><PERSON><PERSON>("Jack", 11),
    <PERSON><PERSON><PERSON>("Queen", 12),
    <PERSON><PERSON>("King", 13);
    
    private final String displayName;
    private final int value;
    
    Rank(String displayName, int value) {
        this.displayName = displayName;
        this.value = value;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
    
    public int getValue() {
        return value;
    }
}

class Card implements Comparable<Card> {
    private final Suit suit;
    private final Rank rank;
    
    public Card(Suit suit, Rank rank) {
        this.suit = suit;
        this.rank = rank;
    }
    
    public Suit getSuit() {
        return suit;
    }
    
    public Rank getRank() {
        return rank;
    }
    
    @Override
    public String toString() {
        return rank + " of " + suit;
    }
    
    @Override
    public int compareTo(Card other) {
        int suitComparison = this.suit.compareTo(other.suit);
        if (suitComparison != 0) {
            return suitComparison;
        }
        return Integer.compare(this.rank.getValue(), other.rank.getValue());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Card card = (Card) obj;
        return suit == card.suit && rank == card.rank;
    }
    
    @Override
    public int hashCode() {
        return suit.hashCode() * 31 + rank.hashCode();
    }
}

class Deck {
    private List<Card> cards;
    private Random random;
    
    public Deck() {
        this.random = new Random();
        reset();
    }
    
    public void reset() {
        cards = new ArrayList<>();

        for (Suit suit : Suit.values()) {
            for (Rank rank : Rank.values()) {
                cards.add(new Card(suit, rank));
            }
        }
    }
    
    public void shuffle() {
        Collections.shuffle(cards, random);
    }
    
    public Card drawCard() {
        if (isEmpty()) {
            throw new IllegalStateException("Cannot draw from an empty deck!");
        }

        return cards.remove(cards.size() - 1);
    }
    
    public List<Card> drawCards(int count) {
        if (count < 0) {
            throw new IllegalArgumentException("Cannot draw negative number of cards!");
        }
        if (count > cards.size()) {
            throw new IllegalArgumentException("Cannot draw " + count + " cards, only " + cards.size() + " remaining!");
        }
        
        List<Card> drawnCards = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            drawnCards.add(drawCard());
        }
        return drawnCards;
    }
    
    public void sort() {
        Collections.sort(cards);
    }
    
    public void sortByRank() {
        cards.sort((card1, card2) -> {
            int rankComparison = Integer.compare(card1.getRank().getValue(), card2.getRank().getValue());
            if (rankComparison != 0) {
                return rankComparison;
            }
            return card1.getSuit().compareTo(card2.getSuit());
        });
    }
    
    public void printDeck() {
        if (isEmpty()) {
            System.out.println("Deck is empty!");
            return;
        }
        
        System.out.println("Deck (" + cards.size() + " cards):");
        for (int i = 0; i < cards.size(); i++) {
            System.out.print(cards.get(i));
            if (i < cards.size() - 1) {
                System.out.print(", ");
                if ((i + 1) % 6 == 0) {
                    System.out.println();
                }
            }
        }
        System.out.println();
    }
    
    public int size() {
        return cards.size();
    }
    
    public boolean isEmpty() {
        return cards.isEmpty();
    }
    
    public List<Card> getCards() {
        return new ArrayList<>(cards);
    }
}

public class DeckOfCardsSimulator {
    
    public static void main(String[] args) {
        System.out.println("=== Deck of Cards Simulator ===\n");

        Deck deck = new Deck();

        System.out.println("1. Initial Deck:");
        deck.printDeck();
        System.out.println();

        System.out.println("2. After Shuffling:");
        deck.shuffle();
        deck.printDeck();
        System.out.println();

        System.out.println("3. Drawing 5 Cards:");
        try {
            List<Card> drawnCards = deck.drawCards(5);
            System.out.println("Drawn cards: ");
            for (Card card : drawnCards) {
                System.out.print(card + ", ");
            }
            System.out.println("\n");
        } catch (Exception e) {
            System.out.println("Error drawing cards: " + e.getMessage());
        }

        System.out.println("4. Remaining Deck after drawing 5 cards:");
        deck.printDeck();
        System.out.println();

        System.out.println("5. After Sorting (by suit then rank):");
        deck.sort();
        deck.printDeck();
        System.out.println();

        System.out.println("6. After Sorting by Rank first:");
        deck.sortByRank();
        deck.printDeck();
        System.out.println();

        System.out.println("7. After Reset:");
        deck.reset();
        deck.printDeck();
        System.out.println();

        System.out.println("8. Error Handling Demo:");
        demonstrateErrorHandling();
        
        System.out.println("\n=== Simulation Complete ===");
    }
    
    private static void demonstrateErrorHandling() {
        Deck emptyDeck = new Deck();

        try {
            emptyDeck.drawCards(52);
            System.out.println("Drew all 52 cards successfully.");

            System.out.println("Attempting to draw from empty deck...");
            emptyDeck.drawCard();

        } catch (IllegalStateException e) {
            System.out.println("Caught expected error: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("Unexpected error: " + e.getMessage());
        }

        Deck newDeck = new Deck();
        try {
            System.out.println("Attempting to draw -1 cards...");
            newDeck.drawCards(-1);
        } catch (IllegalArgumentException e) {
            System.out.println("Caught expected error: " + e.getMessage());
        }

        try {
            System.out.println("Attempting to draw 100 cards from 52-card deck...");
            newDeck.drawCards(100);
        } catch (IllegalArgumentException e) {
            System.out.println("Caught expected error: " + e.getMessage());
        }
    }
}

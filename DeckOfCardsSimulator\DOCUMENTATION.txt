===============================================================================
                        DECK OF CARDS SIMULATOR
                     Complete Java Implementation
===============================================================================

PROJECT OVERVIEW
================
A comprehensive Java implementation of a standard 52-card deck simulator using 
Object-Oriented Programming principles. This project demonstrates clean code 
design, proper encapsulation, and all essential deck operations.

FILES INCLUDED
==============
1. DeckOfCardsSimulator.java - Complete implementation (single file)
2. DOCUMENTATION.txt - This documentation file

COMPILATION AND EXECUTION
=========================
1. Compile the program:
   javac DeckOfCardsSimulator.java

2. Run the program:
   java DeckOfCardsSimulator

SYSTEM REQUIREMENTS
===================
- Java Development Kit (JDK) 8 or higher
- Command line access or Java IDE
- No external dependencies required

FEATURES IMPLEMENTED
===================
✓ Standard 52-card deck initialization (no duplicates)
✓ Shuffle - Randomize card order using Collections.shuffle()
✓ Draw cards - Remove and return cards (single or multiple)
✓ Sort - Sort by suit first, then by rank
✓ Alternative sorting - Sort by rank first, then by suit
✓ Reset - Restore deck to original 52 cards
✓ Display - Print all remaining cards with formatting
✓ Error handling for empty deck scenarios
✓ Multiple card drawing with validation
✓ Clean OOP design with proper encapsulation

CLASSES AND COMPONENTS
=====================

1. SUIT ENUM
   - Represents four suits: Hearts, Diamonds, Spades, Clubs
   - Ordered for traditional sorting
   - Provides string representation

2. RANK ENUM
   - Represents thirteen ranks: Ace through King
   - Includes numeric values (1-13) for sorting
   - Supports both display names and values

3. CARD CLASS
   - Immutable representation of a playing card
   - Implements Comparable for sorting
   - Provides equals() and hashCode() methods
   - String format: "Rank of Suit" (e.g., "Ace of Spades")

4. DECK CLASS
   - Manages collection of 52 cards
   - Supports all required operations
   - Includes error handling and validation
   - Provides defensive copying for card access

5. MAIN CLASS (DeckOfCardsSimulator)
   - Demonstrates all functionality
   - Shows complete workflow
   - Includes error handling examples

KEY METHODS
===========

DECK CLASS METHODS:
- reset() - Initialize/reset deck to 52 cards
- shuffle() - Randomize card order
- drawCard() - Draw single card (throws exception if empty)
- drawCards(int count) - Draw multiple cards with validation
- sort() - Sort by suit then rank
- sortByRank() - Sort by rank then suit
- printDeck() - Display all cards
- size() - Get number of remaining cards
- isEmpty() - Check if deck is empty
- getCards() - Get defensive copy of cards

CARD CLASS METHODS:
- getSuit() - Get card suit
- getRank() - Get card rank
- toString() - Get string representation
- compareTo() - Compare cards for sorting
- equals() - Check card equality
- hashCode() - Get hash code

EXAMPLE USAGE
=============

Basic Usage:
-----------
Deck deck = new Deck();              // Create new deck (52 cards)
deck.shuffle();                      // Shuffle the deck
Card card = deck.drawCard();         // Draw one card
List<Card> hand = deck.drawCards(5); // Draw 5 cards
deck.sort();                         // Sort remaining cards
deck.reset();                        // Reset to full deck

Error Handling:
--------------
try {
    deck.drawCards(100);  // Will throw IllegalArgumentException
} catch (IllegalArgumentException e) {
    System.out.println("Error: " + e.getMessage());
}

try {
    // Draw all cards first
    deck.drawCards(52);
    deck.drawCard();      // Will throw IllegalStateException
} catch (IllegalStateException e) {
    System.out.println("Error: " + e.getMessage());
}

SAMPLE OUTPUT
=============
The program demonstrates:

1. Initial ordered deck (52 cards)
2. Shuffled deck
3. Drawing 5 cards
4. Remaining deck after drawing
5. Sorted deck (by suit then rank)
6. Alternative sorting (by rank then suit)
7. Reset functionality
8. Error handling demonstrations

Example output snippet:
=== Deck of Cards Simulator ===

1. Initial Deck:
Deck (52 cards):
Ace of Hearts, 2 of Hearts, 3 of Hearts, 4 of Hearts, 5 of Hearts, 6 of Hearts,
7 of Hearts, 8 of Hearts, 9 of Hearts, 10 of Hearts, Jack of Hearts, Queen of Hearts,
King of Hearts, Ace of Diamonds, 2 of Diamonds, 3 of Diamonds...

2. After Shuffling:
Deck (52 cards):
7 of Diamonds, Jack of Spades, 3 of Hearts, Queen of Clubs...

DESIGN PRINCIPLES
================

Object-Oriented Programming:
- Encapsulation: Private fields with public methods
- Single Responsibility: Each class has one clear purpose
- Immutability: Card objects cannot be modified after creation
- Composition: Deck contains Cards, Cards contain Suit and Rank

Error Handling:
- Throws appropriate exceptions for invalid operations
- Validates input parameters
- Provides meaningful error messages
- Fails fast on invalid states

Code Quality:
- Comprehensive documentation with comments
- Consistent naming conventions
- Clean, readable code structure
- Proper use of Java collections and generics

EXTENSIONS AND MODIFICATIONS
===========================
The design supports easy extensions such as:
- Different deck sizes (e.g., Pinochle deck with 48 cards)
- Additional sorting criteria
- Card game implementations (Poker, Blackjack, etc.)
- Hand management classes
- Scoring systems
- Multiple deck support

TECHNICAL DETAILS
=================

Performance Characteristics:
- drawCard(): O(1) - removes from end of ArrayList
- shuffle(): O(n) - Collections.shuffle implementation
- sort(): O(n log n) - Collections.sort with Comparable
- reset(): O(1) - creates new ArrayList with 52 cards

Memory Usage:
- Each Card object: ~32 bytes (object overhead + 2 enum references)
- Full deck: ~1.7KB (52 cards + ArrayList overhead)
- Minimal memory footprint for typical use cases

Thread Safety:
- Card objects are immutable (thread-safe)
- Deck class is NOT thread-safe (not synchronized)
- For concurrent use, external synchronization required

TROUBLESHOOTING
===============

Common Issues:
1. "Cannot find symbol" errors
   - Ensure all code is in the same file
   - Check Java version compatibility

2. "IllegalStateException: Cannot draw from an empty deck!"
   - Check deck size before drawing: deck.size() > 0
   - Use deck.isEmpty() to verify deck state

3. "IllegalArgumentException: Cannot draw negative number of cards!"
   - Validate input: count >= 0 && count <= deck.size()

4. Compilation errors
   - Ensure JDK 8+ is installed
   - Verify file name matches public class name

EDUCATIONAL VALUE
================
This project demonstrates:
- Enum usage for type safety
- Immutable object design
- Comparable interface implementation
- Exception handling best practices
- Collections framework usage
- Clean OOP design principles
- Defensive programming techniques

REQUIREMENTS FULFILLED
======================
✓ Card class with suit, rank, and toString()
✓ Deck class with all required methods
✓ Enum usage for suits and ranks
✓ Collections.shuffle() for randomization
✓ Proper error handling
✓ Clean OOP design with encapsulation
✓ Comprehensive comments and documentation
✓ Optional features (multiple card drawing, custom sorting)

VERSION INFORMATION
==================
- Language: Java 8+
- Dependencies: None (uses only standard Java libraries)
- File Count: 1 (single file implementation)
- Lines of Code: ~280
- Classes: 5 (4 inner + 1 public)
- Methods: 20+ total

AUTHOR NOTES
============
This implementation prioritizes:
1. Code clarity and readability
2. Proper OOP design principles
3. Comprehensive error handling
4. Educational value
5. Ease of use and modification

The single-file approach makes it easy to share, compile, and understand
while maintaining all the benefits of proper object-oriented design.

===============================================================================
                            END OF DOCUMENTATION
===============================================================================

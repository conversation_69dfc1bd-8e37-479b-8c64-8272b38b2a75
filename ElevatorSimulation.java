import java.util.Scanner;

/**
 * Main simulation class for the elevator system
 */
public class ElevatorSimulation {
    private static final int SIMULATION_DELAY_MS = 1000; // 1 second between moves
    
    public static void main(String[] args) {
        System.out.println("===== Single-Threaded Elevator Simulation =====\n");
        
        // Run predefined scenarios
        runScenario1();
        System.out.println("\n" + "=".repeat(50) + "\n");
        runScenario2();
        System.out.println("\n" + "=".repeat(50) + "\n");
        runScenario3();
        
        // Interactive mode
        System.out.println("\n" + "=".repeat(50));
        System.out.println("Starting Interactive Mode...");
        runInteractiveMode();
    }
    
    /**
     * Scenario 1: Basic up and down movement
     */
    private static void runScenario1() {
        System.out.println("--- Scenario 1: Basic Movement ---");
        Elevator elevator = new Elevator(1);
        
        // Add some requests
        elevator.addRequest(5, RequestType.INTERNAL);
        elevator.addRequest(3, RequestType.EXTERNAL_DOWN);
        elevator.addRequest(7, RequestType.INTERNAL);
        
        // Run simulation
        runSimulation(elevator, "Scenario 1");
    }
    
    /**
     * Scenario 2: Complex multi-directional requests
     */
    private static void runScenario2() {
        System.out.println("--- Scenario 2: Complex Multi-Directional ---");
        Elevator elevator = new Elevator(10);
        
        // Add requests in different directions
        elevator.addRequest(15, RequestType.INTERNAL);
        elevator.addRequest(5, RequestType.EXTERNAL_UP);
        elevator.addRequest(18, RequestType.INTERNAL);
        elevator.addRequest(2, RequestType.EXTERNAL_DOWN);
        elevator.addRequest(12, RequestType.EXTERNAL_UP);
        
        runSimulation(elevator, "Scenario 2");
    }
    
    /**
     * Scenario 3: Duplicate request handling
     */
    private static void runScenario3() {
        System.out.println("--- Scenario 3: Duplicate Request Handling ---");
        Elevator elevator = new Elevator(5);
        
        // Add requests including duplicates
        elevator.addRequest(8, RequestType.INTERNAL);
        elevator.addRequest(8, RequestType.INTERNAL); // Duplicate
        elevator.addRequest(3, RequestType.EXTERNAL_DOWN);
        elevator.addRequest(8, RequestType.EXTERNAL_UP); // Different type, same floor
        elevator.addRequest(12, RequestType.INTERNAL);
        
        runSimulation(elevator, "Scenario 3");
    }
    
    /**
     * Run the elevator simulation until all requests are processed
     */
    private static void runSimulation(Elevator elevator, String scenarioName) {
        System.out.println("Starting " + scenarioName + "...");
        System.out.println("Initial Status: " + elevator.getStatus());
        System.out.println();
        
        int moveCount = 0;
        int maxMoves = 100; // Safety limit
        
        while (elevator.hasPendingRequests() && moveCount < maxMoves) {
            try {
                elevator.move();
                Thread.sleep(SIMULATION_DELAY_MS); // Delay for realistic simulation
                moveCount++;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.out.println("[ERROR] Simulation interrupted");
                break;
            }
        }
        
        if (moveCount >= maxMoves) {
            System.out.println("[WARNING] Simulation stopped - maximum moves reached");
        }
        
        System.out.println("\n" + scenarioName + " completed!");
        System.out.println("Final Status: " + elevator.getStatus());
        System.out.println("Total moves: " + moveCount);
    }
    
    /**
     * Interactive mode allowing user to add requests in real-time
     */
    private static void runInteractiveMode() {
        Elevator elevator = new Elevator(1);
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("Interactive Elevator Simulation");
        System.out.println("Commands:");
        System.out.println("  'r <floor>' - Add internal request");
        System.out.println("  'u <floor>' - Add external up request");
        System.out.println("  'd <floor>' - Add external down request");
        System.out.println("  's' - Show status");
        System.out.println("  'm' - Move elevator one step");
        System.out.println("  'auto' - Auto-run until all requests processed");
        System.out.println("  'q' - Quit");
        System.out.println();
        
        boolean running = true;
        while (running) {
            System.out.print("Enter command: ");
            String input = scanner.nextLine().trim().toLowerCase();
            
            try {
                if (input.equals("q")) {
                    running = false;
                } else if (input.equals("s")) {
                    System.out.println("Status: " + elevator.getStatus());
                } else if (input.equals("m")) {
                    elevator.move();
                } else if (input.equals("auto")) {
                    System.out.println("Auto-running simulation...");
                    runSimulation(elevator, "Interactive Auto-Run");
                } else if (input.startsWith("r ")) {
                    int floor = Integer.parseInt(input.substring(2));
                    elevator.addRequest(floor, RequestType.INTERNAL);
                } else if (input.startsWith("u ")) {
                    int floor = Integer.parseInt(input.substring(2));
                    elevator.addRequest(floor, RequestType.EXTERNAL_UP);
                } else if (input.startsWith("d ")) {
                    int floor = Integer.parseInt(input.substring(2));
                    elevator.addRequest(floor, RequestType.EXTERNAL_DOWN);
                } else {
                    System.out.println("Unknown command. Type 'q' to quit.");
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid floor number. Please enter a valid integer.");
            } catch (Exception e) {
                System.out.println("Error: " + e.getMessage());
            }
            
            System.out.println();
        }
        
        scanner.close();
        System.out.println("Simulation ended. Thank you!");
    }
}

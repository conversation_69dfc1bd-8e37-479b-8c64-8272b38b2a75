/**
 * Simple test class for the Elevator system without JUnit dependency
 * Run with: java SimpleElevatorTest
 */
public class SimpleElevatorTest {
    private static int testCount = 0;
    private static int passedTests = 0;
    
    public static void main(String[] args) {
        System.out.println("===== Simple Elevator Tests =====\n");
        
        testElevatorInitialization();
        testAddValidRequests();
        testDuplicateRequestPrevention();
        testInvalidFloorRequests();
        testElevatorMovementUp();
        testElevatorMovementDown();
        testMultipleRequestsSameDirection();
        testStatusStringFormat();
        testInvalidStartingFloor();
        testDifferentRequestTypesSameFloor();
        
        System.out.println("\n===== Test Results =====");
        System.out.println("Tests passed: " + passedTests + "/" + testCount);
        System.out.println("Success rate: " + (passedTests * 100 / testCount) + "%");
        
        if (passedTests == testCount) {
            System.out.println("🎉 All tests passed!");
        } else {
            System.out.println("❌ Some tests failed.");
        }
    }
    
    private static void testElevatorInitialization() {
        System.out.println("Test: Elevator Initialization");
        Elevator elevator = new Elevator(1);
        
        boolean passed = true;
        passed &= assertEquals(1, elevator.getCurrentFloor(), "Current floor");
        passed &= assertEquals(Direction.IDLE, elevator.getDirection(), "Direction");
        passed &= assertFalse(elevator.isMoving(), "Is moving");
        passed &= assertFalse(elevator.hasPendingRequests(), "Has pending requests");
        passed &= assertTrue(elevator.getPendingRequests().isEmpty(), "Pending requests empty");
        
        reportTest("Elevator Initialization", passed);
    }
    
    private static void testAddValidRequests() {
        System.out.println("Test: Add Valid Requests");
        Elevator elevator = new Elevator(1);
        
        boolean passed = true;
        passed &= assertTrue(elevator.addRequest(5, RequestType.INTERNAL), "Add request 5");
        passed &= assertTrue(elevator.addRequest(3, RequestType.EXTERNAL_UP), "Add request 3");
        passed &= assertTrue(elevator.hasPendingRequests(), "Has pending requests");
        passed &= assertEquals(2, elevator.getPendingRequests().size(), "Pending count");
        
        reportTest("Add Valid Requests", passed);
    }
    
    private static void testDuplicateRequestPrevention() {
        System.out.println("Test: Duplicate Request Prevention");
        Elevator elevator = new Elevator(1);
        
        boolean passed = true;
        passed &= assertTrue(elevator.addRequest(5, RequestType.INTERNAL), "First request");
        passed &= assertFalse(elevator.addRequest(5, RequestType.INTERNAL), "Duplicate request");
        passed &= assertEquals(1, elevator.getPendingRequests().size(), "Pending count");
        
        reportTest("Duplicate Request Prevention", passed);
    }
    
    private static void testInvalidFloorRequests() {
        System.out.println("Test: Invalid Floor Requests");
        Elevator elevator = new Elevator(1);
        
        boolean passed = true;
        passed &= assertFalse(elevator.addRequest(0, RequestType.INTERNAL), "Below minimum");
        passed &= assertFalse(elevator.addRequest(25, RequestType.INTERNAL), "Above maximum");
        passed &= assertFalse(elevator.addRequest(1, RequestType.INTERNAL), "Current floor");
        passed &= assertFalse(elevator.hasPendingRequests(), "No pending requests");
        
        reportTest("Invalid Floor Requests", passed);
    }
    
    private static void testElevatorMovementUp() {
        System.out.println("Test: Elevator Movement Up");
        Elevator elevator = new Elevator(1);
        elevator.addRequest(3, RequestType.INTERNAL);
        
        boolean passed = true;
        passed &= assertEquals(1, elevator.getCurrentFloor(), "Initial floor");
        passed &= assertEquals(Direction.IDLE, elevator.getDirection(), "Initial direction");
        
        elevator.move(); // Should move to floor 2
        passed &= assertEquals(2, elevator.getCurrentFloor(), "After first move");
        passed &= assertEquals(Direction.UP, elevator.getDirection(), "Direction after move");
        passed &= assertTrue(elevator.isMoving(), "Is moving");
        
        elevator.move(); // Should move to floor 3
        passed &= assertEquals(3, elevator.getCurrentFloor(), "After second move");
        
        reportTest("Elevator Movement Up", passed);
    }
    
    private static void testElevatorMovementDown() {
        System.out.println("Test: Elevator Movement Down");
        Elevator elevator = new Elevator(5);
        elevator.addRequest(2, RequestType.INTERNAL);
        
        boolean passed = true;
        passed &= assertEquals(5, elevator.getCurrentFloor(), "Initial floor");
        
        elevator.move(); // Should move to floor 4
        passed &= assertEquals(4, elevator.getCurrentFloor(), "After first move");
        passed &= assertEquals(Direction.DOWN, elevator.getDirection(), "Direction");
        passed &= assertTrue(elevator.isMoving(), "Is moving");
        
        reportTest("Elevator Movement Down", passed);
    }
    
    private static void testMultipleRequestsSameDirection() {
        System.out.println("Test: Multiple Requests Same Direction");
        Elevator elevator = new Elevator(1);
        
        boolean passed = true;
        passed &= assertTrue(elevator.addRequest(3, RequestType.INTERNAL), "Request 3");
        passed &= assertTrue(elevator.addRequest(5, RequestType.INTERNAL), "Request 5");
        passed &= assertTrue(elevator.addRequest(7, RequestType.INTERNAL), "Request 7");
        passed &= assertEquals(3, elevator.getPendingRequests().size(), "Pending count");
        
        reportTest("Multiple Requests Same Direction", passed);
    }
    
    private static void testStatusStringFormat() {
        System.out.println("Test: Status String Format");
        Elevator elevator = new Elevator(1);
        String status = elevator.getStatus();
        
        boolean passed = true;
        passed &= status.contains("Floor 1");
        passed &= status.contains("Direction: IDLE");
        passed &= status.contains("Moving: false");
        passed &= status.contains("Pending: []");
        
        reportTest("Status String Format", passed);
    }
    
    private static void testInvalidStartingFloor() {
        System.out.println("Test: Invalid Starting Floor");
        boolean passed = true;
        
        try {
            new Elevator(0);
            passed = false; // Should have thrown exception
        } catch (IllegalArgumentException e) {
            // Expected
        }
        
        try {
            new Elevator(25);
            passed = false; // Should have thrown exception
        } catch (IllegalArgumentException e) {
            // Expected
        }
        
        reportTest("Invalid Starting Floor", passed);
    }
    
    private static void testDifferentRequestTypesSameFloor() {
        System.out.println("Test: Different Request Types Same Floor");
        Elevator elevator = new Elevator(1);
        
        boolean passed = true;
        passed &= assertTrue(elevator.addRequest(5, RequestType.INTERNAL), "Internal request");
        passed &= assertTrue(elevator.addRequest(5, RequestType.EXTERNAL_UP), "External up request");
        passed &= assertEquals(2, elevator.getPendingRequests().size(), "Both requests added");
        
        reportTest("Different Request Types Same Floor", passed);
    }
    
    // Helper methods
    private static boolean assertEquals(Object expected, Object actual, String message) {
        if ((expected == null && actual == null) || 
            (expected != null && expected.equals(actual))) {
            System.out.println("  ✓ " + message + ": " + actual);
            return true;
        } else {
            System.out.println("  ✗ " + message + ": expected " + expected + ", got " + actual);
            return false;
        }
    }
    
    private static boolean assertTrue(boolean condition, String message) {
        if (condition) {
            System.out.println("  ✓ " + message + ": true");
            return true;
        } else {
            System.out.println("  ✗ " + message + ": expected true, got false");
            return false;
        }
    }
    
    private static boolean assertFalse(boolean condition, String message) {
        if (!condition) {
            System.out.println("  ✓ " + message + ": false");
            return true;
        } else {
            System.out.println("  ✗ " + message + ": expected false, got true");
            return false;
        }
    }
    
    private static void reportTest(String testName, boolean passed) {
        testCount++;
        if (passed) {
            passedTests++;
            System.out.println("✅ " + testName + " PASSED\n");
        } else {
            System.out.println("❌ " + testName + " FAILED\n");
        }
    }
}

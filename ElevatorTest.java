import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;
import java.util.List;

/**
 * JUnit test class for the Elevator system
 * Note: Requires JUnit 5 dependency
 */
public class ElevatorTest {
    private Elevator elevator;
    
    @BeforeEach
    void setUp() {
        elevator = new Elevator(1); // Start at floor 1
    }
    
    @Test
    @DisplayName("Test elevator initialization")
    void testElevatorInitialization() {
        assertEquals(1, elevator.getCurrentFloor());
        assertEquals(Direction.IDLE, elevator.getDirection());
        assertFalse(elevator.isMoving());
        assertFalse(elevator.hasPendingRequests());
        assertTrue(elevator.getPendingRequests().isEmpty());
    }
    
    @Test
    @DisplayName("Test adding valid requests")
    void testAddValidRequests() {
        assertTrue(elevator.addRequest(5, RequestType.INTERNAL));
        assertTrue(elevator.addRequest(3, RequestType.EXTERNAL_UP));
        assertTrue(elevator.hasPendingRequests());
        
        List<Integer> pending = elevator.getPendingRequests();
        assertEquals(2, pending.size());
        assertTrue(pending.contains(5));
        assertTrue(pending.contains(3));
    }
    
    @Test
    @DisplayName("Test duplicate request prevention")
    void testDuplicateRequestPrevention() {
        assertTrue(elevator.addRequest(5, RequestType.INTERNAL));
        assertFalse(elevator.addRequest(5, RequestType.INTERNAL)); // Duplicate
        
        List<Integer> pending = elevator.getPendingRequests();
        assertEquals(1, pending.size());
        assertEquals(5, pending.get(0).intValue());
    }
    
    @Test
    @DisplayName("Test invalid floor requests")
    void testInvalidFloorRequests() {
        assertFalse(elevator.addRequest(0, RequestType.INTERNAL)); // Below minimum
        assertFalse(elevator.addRequest(25, RequestType.INTERNAL)); // Above maximum
        assertFalse(elevator.addRequest(1, RequestType.INTERNAL)); // Current floor when idle
        
        assertFalse(elevator.hasPendingRequests());
    }
    
    @Test
    @DisplayName("Test elevator movement up")
    void testElevatorMovementUp() {
        elevator.addRequest(3, RequestType.INTERNAL);
        
        // Initial state
        assertEquals(1, elevator.getCurrentFloor());
        assertEquals(Direction.IDLE, elevator.getDirection());
        
        // First move
        elevator.move();
        assertEquals(2, elevator.getCurrentFloor());
        assertEquals(Direction.UP, elevator.getDirection());
        assertTrue(elevator.isMoving());
        
        // Second move
        elevator.move();
        assertEquals(3, elevator.getCurrentFloor());
        assertTrue(elevator.isMoving());
    }
    
    @Test
    @DisplayName("Test elevator movement down")
    void testElevatorMovementDown() {
        // Start elevator at floor 5
        elevator = new Elevator(5);
        elevator.addRequest(2, RequestType.INTERNAL);
        
        // Initial state
        assertEquals(5, elevator.getCurrentFloor());
        
        // First move
        elevator.move();
        assertEquals(4, elevator.getCurrentFloor());
        assertEquals(Direction.DOWN, elevator.getDirection());
        assertTrue(elevator.isMoving());
        
        // Continue moving
        elevator.move();
        assertEquals(3, elevator.getCurrentFloor());
        
        elevator.move();
        assertEquals(2, elevator.getCurrentFloor());
    }
    
    @Test
    @DisplayName("Test elevator becomes idle when no requests")
    void testElevatorBecomesIdle() {
        elevator.addRequest(2, RequestType.INTERNAL);
        
        // Move to floor 2
        elevator.move(); // Floor 2
        assertTrue(elevator.hasPendingRequests());
        
        // Process the floor (this should remove the request)
        try {
            elevator.processFloor();
        } catch (InterruptedException e) {
            fail("Process floor should not be interrupted in test");
        }
        
        // Next move should make elevator idle
        elevator.move();
        assertEquals(Direction.IDLE, elevator.getDirection());
        assertFalse(elevator.isMoving());
        assertFalse(elevator.hasPendingRequests());
    }
    
    @Test
    @DisplayName("Test multiple requests in same direction")
    void testMultipleRequestsSameDirection() {
        elevator.addRequest(3, RequestType.INTERNAL);
        elevator.addRequest(5, RequestType.INTERNAL);
        elevator.addRequest(7, RequestType.INTERNAL);
        
        List<Integer> pending = elevator.getPendingRequests();
        assertEquals(3, pending.size());
        assertTrue(pending.contains(3));
        assertTrue(pending.contains(5));
        assertTrue(pending.contains(7));
    }
    
    @Test
    @DisplayName("Test status string format")
    void testStatusStringFormat() {
        String status = elevator.getStatus();
        assertTrue(status.contains("Floor 1"));
        assertTrue(status.contains("Direction: IDLE"));
        assertTrue(status.contains("Moving: false"));
        assertTrue(status.contains("Pending: []"));
    }
    
    @Test
    @DisplayName("Test constructor with invalid starting floor")
    void testInvalidStartingFloor() {
        assertThrows(IllegalArgumentException.class, () -> new Elevator(0));
        assertThrows(IllegalArgumentException.class, () -> new Elevator(25));
    }
    
    @Test
    @DisplayName("Test different request types for same floor")
    void testDifferentRequestTypesSameFloor() {
        assertTrue(elevator.addRequest(5, RequestType.INTERNAL));
        assertTrue(elevator.addRequest(5, RequestType.EXTERNAL_UP)); // Different type, should be allowed
        
        assertEquals(2, elevator.getPendingRequests().size());
    }
}
